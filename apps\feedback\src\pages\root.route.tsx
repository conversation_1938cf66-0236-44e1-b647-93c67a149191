import useSession from "@/unmatched/modules/session/hook";
import { useEffect } from "react";
import { useNavigate } from "react-router";
import LayoutDemo from "./LayoutDemo";
import ModalDemo from "./ModalDemo";

const RootPath = [
  {
    path: "/",
    element: <Root />,
  },
  {
    path: "/layout-demo",
    element: <LayoutDemo />,
  },
  {
    path: "/modal-demo",
    element: <ModalDemo />,
  },
];

function Root() {
  const { user } = useSession();
  const navigate = useNavigate();
  useEffect(() => {

    if (user.role === "ADMIN") {
      navigate("/admin");
    } else {
      navigate("/user");
    }
    //eslint-disable-next-line
  }, [user]);

  return <>Loading...</>;
}
export default RootPath;
