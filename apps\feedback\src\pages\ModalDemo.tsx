import React, { useState } from "react";
import { Modal } from "@repo/ui/components/modal";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import Layout from "@repo/ui/components/layout";

function ModalDemo() {
  const [basicModal, setBasicModal] = useState(false);
  const [centeredModal, setCenteredModal] = useState(false);
  const [staticModal, setStaticModal] = useState(false);
  const [smallModal, setSmallModal] = useState(false);
  const [largeModal, setLargeModal] = useState(false);
  const [fullscreenModal, setFullscreenModal] = useState(false);
  const [noAnimationModal, setNoAnimationModal] = useState(false);
  const [formModal, setFormModal] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <Layout.Container>
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Modal Component Demo
          </h1>
          
          <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              Modal Examples
            </h2>
            <p className="text-gray-600 mb-6">
              This Modal component is built with Tailwind CSS and shadcn/ui, providing the same API as react-bootstrap Modal for seamless migration.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Basic Modal */}
              <Button onClick={() => setBasicModal(true)} className="w-full">
                Basic Modal
              </Button>

              {/* Centered Modal */}
              <Button onClick={() => setCenteredModal(true)} className="w-full">
                Centered Modal
              </Button>

              {/* Static Backdrop */}
              <Button onClick={() => setStaticModal(true)} className="w-full">
                Static Backdrop
              </Button>

              {/* Small Modal */}
              <Button onClick={() => setSmallModal(true)} className="w-full">
                Small Modal
              </Button>

              {/* Large Modal */}
              <Button onClick={() => setLargeModal(true)} className="w-full">
                Large Modal
              </Button>

              {/* Fullscreen Modal */}
              <Button onClick={() => setFullscreenModal(true)} className="w-full">
                Fullscreen Modal
              </Button>

              {/* No Animation */}
              <Button onClick={() => setNoAnimationModal(true)} className="w-full">
                No Animation
              </Button>

              {/* Form Modal */}
              <Button onClick={() => setFormModal(true)} className="w-full">
                Form Modal
              </Button>
            </div>
          </div>

          {/* Usage Examples */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              Usage Examples
            </h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Basic Usage</h3>
                <pre className="bg-gray-900 text-green-400 p-4 rounded-md text-sm overflow-x-auto">
{`import { Modal } from "@repo/ui/components/modal";

<Modal show={show} onHide={() => setShow(false)}>
  <Modal.Header closeButton>
    <Modal.Title>Modal Title</Modal.Title>
  </Modal.Header>
  <Modal.Body>
    Modal content goes here...
  </Modal.Body>
  <Modal.Footer>
    <Button onClick={() => setShow(false)}>Close</Button>
  </Modal.Footer>
</Modal>`}
                </pre>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Migration from react-bootstrap</h3>
                <div className="bg-gray-100 p-4 rounded-md">
                  <p className="text-sm text-gray-700 mb-2">
                    <strong>Replace this:</strong>
                  </p>
                  <pre className="bg-gray-800 text-red-400 p-2 rounded text-xs mb-2">
{`import { Modal } from "react-bootstrap";`}
                  </pre>
                  <p className="text-sm text-gray-700 mb-2">
                    <strong>With this:</strong>
                  </p>
                  <pre className="bg-gray-800 text-green-400 p-2 rounded text-xs">
{`import { Modal } from "@repo/ui/components/modal";`}
                  </pre>
                  <p className="text-sm text-gray-600 mt-2">
                    All props and usage remain exactly the same!
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Props Compatibility</h3>
                <div className="bg-gray-100 p-4 rounded-md">
                  <p className="text-sm text-gray-700 mb-2">
                    <strong>All react-bootstrap Modal props are supported:</strong>
                  </p>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• <code>show</code> - Controls modal visibility</li>
                    <li>• <code>onHide</code> - Callback when modal should close</li>
                    <li>• <code>backdrop</code> - true, false, or "static"</li>
                    <li>• <code>keyboard</code> - Enable/disable ESC key</li>
                    <li>• <code>animation</code> - Enable/disable animations</li>
                    <li>• <code>centered</code> - Vertically center modal</li>
                    <li>• <code>size</code> - "sm", "lg", "xl"</li>
                    <li>• <code>fullscreen</code> - true or breakpoint string</li>
                    <li>• <code>dialogClassName</code> - Custom CSS class</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Layout.Container>

      {/* Basic Modal */}
      <Modal show={basicModal} onHide={() => setBasicModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Basic Modal</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>This is a basic modal with header, body, and footer.</p>
          <p>It behaves exactly like the react-bootstrap Modal component.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setBasicModal(false)}>
            Close
          </Button>
          <Button onClick={() => setBasicModal(false)}>
            Save Changes
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Centered Modal */}
      <Modal show={centeredModal} onHide={() => setCenteredModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Centered Modal</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>This modal is vertically centered on the screen.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setCenteredModal(false)}>Close</Button>
        </Modal.Footer>
      </Modal>

      {/* Static Backdrop Modal */}
      <Modal 
        show={staticModal} 
        onHide={() => setStaticModal(false)} 
        backdrop="static"
        keyboard={false}
      >
        <Modal.Header closeButton>
          <Modal.Title>Static Backdrop</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>This modal won't close when clicking outside or pressing ESC.</p>
          <p>You must use the close button or the X button.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setStaticModal(false)}>Close</Button>
        </Modal.Footer>
      </Modal>

      {/* Small Modal */}
      <Modal show={smallModal} onHide={() => setSmallModal(false)} size="sm">
        <Modal.Header closeButton>
          <Modal.Title>Small Modal</Modal.Title>
        </Modal.Header>
        <Modal.Body size="sm">
          <p>This is a small modal.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setSmallModal(false)}>Close</Button>
        </Modal.Footer>
      </Modal>

      {/* Large Modal */}
      <Modal show={largeModal} onHide={() => setLargeModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Large Modal</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>This is a large modal with more space for content.</p>
          <p>Perfect for forms, detailed information, or complex layouts.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setLargeModal(false)}>Close</Button>
        </Modal.Footer>
      </Modal>

      {/* Fullscreen Modal */}
      <Modal show={fullscreenModal} onHide={() => setFullscreenModal(false)} fullscreen>
        <Modal.Header closeButton>
          <Modal.Title>Fullscreen Modal</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>This modal takes up the entire screen.</p>
          <p>Great for immersive experiences or complex interfaces.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setFullscreenModal(false)}>Close</Button>
        </Modal.Footer>
      </Modal>

      {/* No Animation Modal */}
      <Modal 
        show={noAnimationModal} 
        onHide={() => setNoAnimationModal(false)} 
        animation={false}
      >
        <Modal.Header closeButton>
          <Modal.Title>No Animation</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>This modal appears instantly without animations.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setNoAnimationModal(false)}>Close</Button>
        </Modal.Footer>
      </Modal>

      {/* Form Modal */}
      <Modal show={formModal} onHide={() => setFormModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Contact Form</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Name</Label>
              <Input id="name" placeholder="Enter your name" />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" placeholder="Enter your email" />
            </div>
            <div>
              <Label htmlFor="message">Message</Label>
              <Textarea id="message" placeholder="Enter your message" rows={4} />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setFormModal(false)}>
            Cancel
          </Button>
          <Button onClick={() => setFormModal(false)}>
            Send Message
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}

export default ModalDemo;
