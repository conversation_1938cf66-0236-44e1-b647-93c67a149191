"use client"

import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { XIcon } from "lucide-react"
import { cn } from "../lib/utils"

// Main Modal component that wraps the Dialog
interface ModalProps extends React.ComponentProps<typeof DialogPrimitive.Root> {
  show?: boolean
  onHide?: () => void
  backdrop?: boolean | "static"
  keyboard?: boolean
  animation?: boolean
  centered?: boolean
  size?: "sm" | "lg" | "xl"
  fullscreen?: boolean | string
  dialogClassName?: string
  backdropClassName?: string
  className?: string
  children?: React.ReactNode
}

function Modal({
  show = false,
  onHide,
  backdrop = true,
  keyboard = true,
  animation = true,
  centered = false,
  size,
  fullscreen = false,
  dialogClassName,
  backdropClassName,
  className,
  children,
  ...props
}: ModalProps) {
  const handleOpenChange = (open: boolean) => {
    if (!open && onHide) {
      onHide()
    }
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (backdrop === "static") {
      e.preventDefault()
      return
    }
    if (backdrop === false) {
      return
    }
    if (onHide) {
      onHide()
    }
  }

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === "Escape" && keyboard && onHide) {
      onHide()
    }
  }

  React.useEffect(() => {
    if (show && keyboard) {
      document.addEventListener("keydown", handleKeyDown)
      return () => document.removeEventListener("keydown", handleKeyDown)
    }
  }, [show, keyboard])

  return (
    <DialogPrimitive.Root 
      open={show} 
      onOpenChange={handleOpenChange}
      {...props}
    >
      <DialogPrimitive.Portal>
        <DialogPrimitive.Overlay
          className={cn(
            "fixed inset-0 z-50 bg-black/50",
            animation && "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
            backdropClassName
          )}
          onClick={handleBackdropClick}
        />
        <DialogPrimitive.Content
          className={cn(
            "fixed left-[50%] top-[50%] z-50 translate-x-[-50%] translate-y-[-50%] bg-white rounded-lg shadow-lg",
            animation && "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]",
            centered && "top-[50%]",
            !fullscreen && size === "sm" && "max-w-sm w-full mx-4",
            !fullscreen && size === "lg" && "max-w-4xl w-full mx-4",
            !fullscreen && size === "xl" && "max-w-6xl w-full mx-4",
            !fullscreen && !size && "max-w-lg w-full mx-4",
            fullscreen === true && "w-screen h-screen max-w-none rounded-none",
            typeof fullscreen === "string" && fullscreen.includes("sm") && "sm:w-screen sm:h-screen sm:max-w-none sm:rounded-none",
            typeof fullscreen === "string" && fullscreen.includes("md") && "md:w-screen md:h-screen md:max-w-none md:rounded-none",
            typeof fullscreen === "string" && fullscreen.includes("lg") && "lg:w-screen lg:h-screen lg:max-w-none lg:rounded-none",
            typeof fullscreen === "string" && fullscreen.includes("xl") && "xl:w-screen xl:h-screen xl:max-w-none xl:rounded-none",
            dialogClassName,
            className
          )}
        >
          {children}
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </DialogPrimitive.Root>
  )
}

// Modal Header component
interface ModalHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  closeButton?: boolean
  onHide?: () => void
}

function ModalHeader({ 
  closeButton = false, 
  onHide, 
  className, 
  children, 
  ...props 
}: ModalHeaderProps) {
  return (
    <div
      className={cn(
        "flex items-center justify-center px-6 py-4 bg-gray-100 border-b border-gray-200 rounded-t-lg relative",
        className
      )}
      {...props}
    >
      <div className="flex-1 text-center">
        {children}
      </div>
      {closeButton && (
        <button
          onClick={onHide}
          className="absolute right-4 top-1/2 -translate-y-1/2 bg-gray-300 hover:bg-gray-400 rounded w-7 h-7 flex items-center justify-center text-gray-600 hover:text-gray-800 transition-colors"
          type="button"
        >
          <XIcon className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </button>
      )}
    </div>
  )
}

// Modal Title component
interface ModalTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {}

function ModalTitle({ className, ...props }: ModalTitleProps) {
  return (
    <h2
      className={cn(
        "text-sm font-semibold text-gray-900",
        className
      )}
      {...props}
    />
  )
}

// Modal Body component
interface ModalBodyProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "lg" | "xl"
}

function ModalBody({ size, className, ...props }: ModalBodyProps) {
  return (
    <div
      className={cn(
        "px-6 py-4 overflow-auto",
        size !== "sm" && "h-[61vh]",
        className
      )}
      {...props}
    />
  )
}

// Modal Footer component
interface ModalFooterProps extends React.HTMLAttributes<HTMLDivElement> {}

function ModalFooter({ className, ...props }: ModalFooterProps) {
  return (
    <div
      className={cn(
        "px-6 py-4 min-h-[66px] border-t-0 rounded-b-lg",
        className
      )}
      {...props}
    />
  )
}

// Attach sub-components as static properties
Modal.Header = ModalHeader
Modal.Title = ModalTitle
Modal.Body = ModalBody
Modal.Footer = ModalFooter

export { Modal, ModalHeader, ModalTitle, ModalBody, ModalFooter }
